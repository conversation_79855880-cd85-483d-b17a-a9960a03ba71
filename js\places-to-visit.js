/**
 * Places to Visit JavaScript - Interactive Functionality
 * Handles filtering, animations, and card interactions
 */

(function() {
    'use strict';

    // ========================================
    // Configuration
    // ========================================

    const CONFIG = {
        ANIMATION_DURATION: 300,
        STAGGER_DELAY: 100,
        SCROLL_THRESHOLD: 100
    };

    // ========================================
    // DOM Elements
    // ========================================

    let elements = {};

    // ========================================
    // State Management
    // ========================================

    const state = {
        currentFilter: 'all',
        isAnimating: false,
        visibleCards: []
    };

    // ========================================
    // Utility Functions
    // ========================================

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // ========================================
    // Animation Functions
    // ========================================

    function animateCardIn(card, delay = 0) {
        return new Promise((resolve) => {
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
                card.style.transition = `opacity ${CONFIG.ANIMATION_DURATION}ms ease, transform ${CONFIG.ANIMATION_DURATION}ms ease`;

                setTimeout(resolve, CONFIG.ANIMATION_DURATION);
            }, delay);
        });
    }

    function animateCardOut(card) {
        return new Promise((resolve) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = `opacity ${CONFIG.ANIMATION_DURATION}ms ease, transform ${CONFIG.ANIMATION_DURATION}ms ease`;

            setTimeout(resolve, CONFIG.ANIMATION_DURATION);
        });
    }

    // ========================================
    // Filter Functions
    // ========================================

    function countCardsByCategory() {
        const counts = {
            all: 0,
            historical: 0,
            religious: 0,
            natural: 0,
            cultural: 0
        };

        elements.placeCards.forEach(card => {
            const category = card.dataset.category;
            counts.all++;
            if (counts[category] !== undefined) {
                counts[category]++;
            }
        });

        return counts;
    }

    function updateFilterButtonCounts() {
        const counts = countCardsByCategory();

        elements.filterButtons.forEach(button => {
            const category = button.dataset.filter;
            const count = counts[category] || 0;
            const buttonText = button.textContent.split('(')[0].trim(); // Remove existing count
            button.innerHTML = `${buttonText} <span class="filter-count">(${count})</span>`;
        });
    }

    function updateActiveFilterCount(category, count) {
        // Update the current count in stats
        const currentCountElement = document.querySelector('.current-count');
        if (currentCountElement) {
            currentCountElement.textContent = count;
        }

        // Update the stats text
        const statsTextElement = document.querySelector('.stats-text');
        if (statsTextElement) {
            if (category === 'all') {
                statsTextElement.innerHTML = `Showing <span class="current-count">${count}</span> amazing places`;
            } else {
                const categoryNames = {
                    historical: 'Historical',
                    religious: 'Religious',
                    natural: 'Nature',
                    cultural: 'Cultural'
                };
                statsTextElement.innerHTML = `Showing <span class="current-count">${count}</span> ${categoryNames[category]} places`;
            }
        }

        // Update the places subtitle with filter info
        const placesSubtitle = document.querySelector('.places-subtitle');
        if (placesSubtitle) {
            if (category === 'all') {
                placesSubtitle.textContent = `From historical monuments to natural wonders, explore the best destinations around Ahmedabad`;
            } else {
                const categoryNames = {
                    historical: 'Historical',
                    religious: 'Religious',
                    natural: 'Nature',
                    cultural: 'Cultural'
                };
                placesSubtitle.textContent = `Discover amazing ${categoryNames[category].toLowerCase()} places and attractions around Ahmedabad`;
            }
        }
    }

    function filterPlaces(category) {
        if (state.isAnimating) return;

        state.isAnimating = true;
        state.currentFilter = category;

        const cards = elements.placeCards;
        const visibleCards = [];
        const hiddenCards = [];

        // Categorize cards
        cards.forEach(card => {
            const cardCategory = card.dataset.category;
            if (category === 'all' || cardCategory === category) {
                visibleCards.push(card);
            } else {
                hiddenCards.push(card);
            }
        });

        // Animate out hidden cards first
        Promise.all(hiddenCards.map(card => animateCardOut(card)))
            .then(() => {
                // Hide cards completely
                hiddenCards.forEach(card => {
                    card.style.display = 'none';
                });

                // Show and animate in visible cards
                visibleCards.forEach((card, index) => {
                    card.style.display = 'flex';
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                });

                return Promise.all(
                    visibleCards.map((card, index) =>
                        animateCardIn(card, index * CONFIG.STAGGER_DELAY)
                    )
                );
            })
            .then(() => {
                state.isAnimating = false;
                state.visibleCards = visibleCards;

                // Update active filter count display
                updateActiveFilterCount(category, visibleCards.length);

                // Update URL without page reload
                updateURL(category);

                // Update analytics if available
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'filter_places', {
                        'event_category': 'Places',
                        'event_label': category,
                        'value': visibleCards.length
                    });
                }
            });
    }

    function updateURL(category) {
        const url = new URL(window.location);
        if (category === 'all') {
            url.searchParams.delete('filter');
        } else {
            url.searchParams.set('filter', category);
        }
        window.history.replaceState({}, '', url);
    }

    function getFilterFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('filter') || 'all';
    }

    // ========================================
    // Event Handlers
    // ========================================

    function handleFilterClick(event) {
        event.preventDefault();

        const button = event.target.closest('.filter-btn');
        if (!button) return;

        const category = button.dataset.filter;

        // Update active state
        elements.filterButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        // Filter places
        filterPlaces(category);
    }

    function handleCardHover(event) {
        const card = event.currentTarget;

        if (event.type === 'mouseenter') {
            card.style.transform = 'translateY(-10px)';
        } else {
            card.style.transform = 'translateY(0)';
        }
    }

    // ========================================
    // Scroll Animations
    // ========================================

    function handleScroll() {
        const cards = elements.placeCards;

        cards.forEach(card => {
            const rect = card.getBoundingClientRect();
            const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

            if (isVisible && !card.classList.contains('animated')) {
                card.classList.add('animated');
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }
        });
    }

    // ========================================
    // Initialization Functions
    // ========================================

    function initializeElements() {
        elements = {
            filterButtons: document.querySelectorAll('.filter-btn'),
            placeCards: document.querySelectorAll('.place-card'),
            placesGrid: document.querySelector('.places-grid')
        };
    }

    function initializeEventListeners() {
        // Filter buttons
        elements.filterButtons.forEach(button => {
            button.addEventListener('click', handleFilterClick);
        });

        // Place cards hover effects
        elements.placeCards.forEach(card => {
            card.addEventListener('mouseenter', handleCardHover);
            card.addEventListener('mouseleave', handleCardHover);
        });

        // Scroll animations
        window.addEventListener('scroll', throttle(handleScroll, 16));
    }

    function initializeFromURL() {
        const filterFromURL = getFilterFromURL();

        // Update filter button counts first
        updateFilterButtonCounts();

        // Set active filter button
        elements.filterButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.filter === filterFromURL) {
                btn.classList.add('active');
            }
        });

        // Apply filter and update count display
        if (filterFromURL !== 'all') {
            filterPlaces(filterFromURL);
        } else {
            // Update count for 'all' category
            const totalCount = elements.placeCards.length;
            updateActiveFilterCount('all', totalCount);
        }
    }

    function initializeAnimations() {
        // Set initial state for cards
        elements.placeCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            // Stagger animation
            setTimeout(() => {
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // ========================================
    // Main Initialization
    // ========================================

    function init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        try {
            initializeElements();
            initializeEventListeners();
            initializeFromURL();
            initializeAnimations();

            console.log('Places to Visit page initialized successfully');
        } catch (error) {
            console.error('Error initializing Places to Visit page:', error);
        }
    }

    // Start initialization
    init();

})();

    // ========================================
    // Event Handlers
    // ========================================

    function handleFilterClick(event) {
        event.preventDefault();

        const button = event.target.closest('.filter-btn');
        if (!button) return;

        const category = button.dataset.filter;

        // Update active state
        elements.filterButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        // Filter places
        filterPlaces(category);
    }

    function handleCardHover(event) {
        const card = event.currentTarget;
        const image = card.querySelector('.place-image');

        if (event.type === 'mouseenter') {
            // Add subtle parallax effect
            card.addEventListener('mousemove', handleCardMouseMove);
        } else {
            // Remove parallax effect
            card.removeEventListener('mousemove', handleCardMouseMove);
            card.style.transform = '';
        }
    }

    function handleCardMouseMove(event) {
        const card = event.currentTarget;
        const rect = card.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        const rotateX = (y - centerY) / 20;
        const rotateY = (centerX - x) / 20;

        card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-10px)`;
    }

    function handleLearnMoreClick(event) {
        event.preventDefault();

        const button = event.currentTarget;
        const card = button.closest('.place-card');
        const title = card.querySelector('.place-title').textContent;

        // Show modal or navigate to detail page
        showPlaceModal(card);

        // Analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'learn_more_click', {
                'event_category': 'Places',
                'event_label': title
            });
        }
    }

    function showPlaceModal(card) {
        // Create modal if it doesn't exist
        if (!elements.modal) {
            createModal();
        }

        const title = card.querySelector('.place-title').textContent;
        const description = card.querySelector('.place-description').textContent;
        const image = card.querySelector('.place-image').src;
        const distance = card.querySelector('.place-distance').textContent;
        const category = card.querySelector('.place-category-badge').textContent;

        // Populate modal content
        elements.modal.querySelector('.modal-title').textContent = title;
        elements.modal.querySelector('.modal-description').textContent = description;
        elements.modal.querySelector('.modal-image').src = image;
        elements.modal.querySelector('.modal-distance').textContent = distance;
        elements.modal.querySelector('.modal-category').textContent = category;

        // Show modal
        elements.modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    function createModal() {
        const modalHTML = `
            <div class="place-modal" id="placeModal">
                <div class="modal-overlay"></div>
                <div class="modal-content">
                    <button class="modal-close" aria-label="Close modal">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="modal-image-container">
                        <img src="" alt="" class="modal-image">
                        <div class="modal-category-badge">
                            <span class="modal-category"></span>
                        </div>
                    </div>
                    <div class="modal-body">
                        <h3 class="modal-title"></h3>
                        <p class="modal-description"></p>
                        <div class="modal-meta">
                            <span class="modal-distance">
                                <i class="fas fa-map-marker-alt"></i>
                                <span class="distance-text"></span>
                            </span>
                            <a href="#" class="modal-directions-btn">
                                <i class="fas fa-directions"></i>
                                Get Directions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        elements.modal = document.getElementById('placeModal');

        // Add event listeners
        elements.modal.querySelector('.modal-close').addEventListener('click', closeModal);
        elements.modal.querySelector('.modal-overlay').addEventListener('click', closeModal);

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && elements.modal.classList.contains('active')) {
                closeModal();
            }
        });
    }

    function closeModal() {
        if (elements.modal) {
            elements.modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }