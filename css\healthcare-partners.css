/* =============================================
   IMA NATCON 2025 - Healthcare Partners Page
   Extracted from internal styles
   ============================================= */

/* Healthcare Partners Main Section */
.healthcare-partners-main {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.healthcare-partners-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/assets/icons/pattern-1.jpg') repeat;
    opacity: 0.03;
    z-index: 1;
}

.section-content {
    position: relative;
    z-index: 2;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: white;
    font-size: 32px;
    box-shadow: 0 10px 30px rgba(11, 76, 122, 0.2);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.section-title {
    font-size: 42px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 16px;
    line-height: 1.2;
}

.section-description {
    font-size: 18px;
    line-height: 1.7;
    color: #5a6c7d;
    max-width: 800px;
    margin: 0 auto;
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
    margin-top: 50px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.partner-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(11, 76, 122, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: left;
    position: relative;
    overflow: hidden;
}

.partner-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 40px rgba(11, 76, 122, 0.15);
    border-color: rgba(11, 76, 122, 0.2);
}

.partner-card-header {
    padding: 28px 28px 0 28px;
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 20px;
}

.partner-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #0B4C7A 0%, #1e88e5 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
    box-shadow: 0 8px 24px rgba(11, 76, 122, 0.25);
}

.partner-info {
    flex: 1;
    min-width: 0;
}

.partner-name {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 4px;
    line-height: 1.2;
}

.partner-full-name {
    font-size: 14px;
    font-weight: 500;
    color: #0B4C7A;
    margin-bottom: 0;
    opacity: 0.9;
}

.partner-content {
    padding: 0 28px 28px 28px;
}

.partner-description {
    font-size: 15px;
    line-height: 1.6;
    color: #666666;
    margin-bottom: 24px;
    font-weight: 400;
}

.partner-specialties {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.specialty-tag {
    background: rgba(11, 76, 122, 0.08);
    color: #0B4C7A;
    padding: 8px 14px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(11, 76, 122, 0.15);
    transition: all 0.3s ease;
}

.specialty-tag:hover {
    background: #0B4C7A;
    color: white;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-title {
        font-size: 32px;
    }

    .section-description {
        font-size: 16px;
    }

    .partners-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .partner-card-header {
        padding: 24px 24px 0 24px;
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .partner-content {
        padding: 0 24px 24px 24px;
        text-align: center;
    }

    .partner-icon {
        width: 56px;
        height: 56px;
        font-size: 20px;
        align-self: center;
    }

    .partner-name {
        font-size: 22px;
    }

    .partner-full-name {
        font-size: 13px;
    }

    .partner-description {
        font-size: 14px;
    }

    .partner-specialties {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .healthcare-partners-main {
        padding: 60px 0;
    }

    .section-title {
        font-size: 28px;
    }

    .partners-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .partner-card-header {
        padding: 20px 20px 0 20px;
        gap: 12px;
        align-items: center;
    }

    .partner-content {
        padding: 0 20px 20px 20px;
    }

    .partner-icon {
        width: 48px;
        height: 48px;
        font-size: 18px;
        border-radius: 12px;
    }

    .partner-name {
        font-size: 20px;
    }

    .partner-full-name {
        font-size: 12px;
    }

    .partner-description {
        font-size: 13px;
        margin-bottom: 20px;
    }

    .specialty-tag {
        font-size: 11px;
        padding: 6px 12px;
        border-radius: 10px;
    }
}
