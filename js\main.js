/**
 * IMA NATCON 2025 - Main JavaScript File
 * Handles navigation, mobile menu, and interactive features
 */

(function() {
    'use strict';

    // ========================================
    // Constants and Configuration
    // ========================================

    const CONFIG = {
        SCROLL_THRESHOLD: 50,
        DEBOUNCE_DELAY: 16,
        MOBILE_BREAKPOINT: 768,
        ANIMATION_DURATION: 300
    };

    // ========================================
    // DOM Elements
    // ========================================

    const elements = {
        header: null,
        mainNav: null,
        mobileToggle: null,
        mobileMenuOverlay: null,
        mobileMenu: null,
        mobileClose: null,
        navLinks: null,
        dropdowns: null
    };

    // ========================================
    // State Management
    // ========================================

    const state = {
        isScrolled: false,
        isMobileMenuOpen: false,
        activeDropdown: null,
        scrollPosition: 0
    };

    // ========================================
    // Utility Functions
    // ========================================

    /**
     * Debounce function to limit function calls
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle function for scroll events
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Check if device is mobile
     */
    function isMobile() {
        return window.innerWidth <= CONFIG.MOBILE_BREAKPOINT;
    }

    /**
     * Get scroll position
     */
    function getScrollPosition() {
        return window.pageYOffset || document.documentElement.scrollTop;
    }

    /**
     * Add/remove class with animation support
     */
    function toggleClass(element, className, add) {
        if (!element) return;

        if (add) {
            element.classList.add(className);
        } else {
            element.classList.remove(className);
        }
    }

    // ========================================
    // Header Scroll Effects
    // ========================================

    /**
     * Handle header scroll effects
     */
    function handleHeaderScroll() {
        const currentScrollPosition = getScrollPosition();
        const shouldBeScrolled = currentScrollPosition > CONFIG.SCROLL_THRESHOLD;

        if (shouldBeScrolled !== state.isScrolled) {
            state.isScrolled = shouldBeScrolled;
            toggleClass(elements.header, 'scrolled', shouldBeScrolled);
        }

        state.scrollPosition = currentScrollPosition;
    }

    // ========================================
    // Mobile Menu Functions
    // ========================================

    /**
     * Enhanced Dropdown Handling
     */
    function initDropdownHandling() {
        // First, clean up any existing dropdown behaviors
        cleanupDropdowns();
        
        const dropdownItems = document.querySelectorAll('.nav-item.dropdown');
        
        dropdownItems.forEach(dropdown => {
            let hideTimeout;
            const dropdownMenu = dropdown.querySelector('.dropdown-menu');
            const navLink = dropdown.querySelector('.nav-link');
            
            if (!dropdownMenu || !navLink) return;
            
            // Reset any inline styles first
            dropdownMenu.style.opacity = '';
            dropdownMenu.style.visibility = '';
            dropdownMenu.style.transform = '';
            dropdownMenu.style.pointerEvents = '';
            
            // Add data attribute to track initialization
            dropdown.setAttribute('data-dropdown-initialized', 'true');
            
            // Show dropdown only on specific nav item hover
            dropdown.addEventListener('mouseenter', (e) => {
                // Only proceed if hovering over this specific dropdown
                if (!dropdown.contains(e.target)) return;
                
                clearTimeout(hideTimeout);
                
                // Hide all other dropdowns first
                document.querySelectorAll('.nav-item.dropdown .dropdown-menu').forEach(menu => {
                    if (menu !== dropdownMenu) {
                        menu.style.opacity = '0';
                        menu.style.visibility = 'hidden';
                        menu.style.transform = 'translateY(-5px)';
                        menu.style.pointerEvents = 'none';
                    }
                });
                
                // Show this dropdown
                dropdownMenu.style.opacity = '1';
                dropdownMenu.style.visibility = 'visible';
                dropdownMenu.style.transform = 'translateY(0)';
                dropdownMenu.style.pointerEvents = 'auto';
            });
            
            // Hide dropdown with delay when leaving the entire dropdown area
            dropdown.addEventListener('mouseleave', (e) => {
                // Check if we're still within the dropdown area
                if (dropdown.contains(e.relatedTarget)) return;
                
                hideTimeout = setTimeout(() => {
                    dropdownMenu.style.opacity = '0';
                    dropdownMenu.style.visibility = 'hidden';
                    dropdownMenu.style.transform = 'translateY(-5px)';
                    dropdownMenu.style.pointerEvents = 'none';
                }, 150);
            });
            
            // Keep dropdown open when hovering over dropdown menu itself
            dropdownMenu.addEventListener('mouseenter', () => {
                clearTimeout(hideTimeout);
            });
            
            // Hide when leaving dropdown menu
            dropdownMenu.addEventListener('mouseleave', (e) => {
                // Check if we're moving to parent dropdown
                if (dropdown.contains(e.relatedTarget)) return;
                
                hideTimeout = setTimeout(() => {
                    dropdownMenu.style.opacity = '0';
                    dropdownMenu.style.visibility = 'hidden';
                    dropdownMenu.style.transform = 'translateY(-5px)';
                    dropdownMenu.style.pointerEvents = 'none';
                }, 100);
            });
        });
        
        // Global click handler to close all dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.nav-item.dropdown')) {
                document.querySelectorAll('.nav-item.dropdown .dropdown-menu').forEach(menu => {
                    menu.style.opacity = '0';
                    menu.style.visibility = 'hidden';
                    menu.style.transform = 'translateY(-5px)';
                    menu.style.pointerEvents = 'none';
                });
            }
        });
    }

    /**
     * Initialize Submenu Handling - SPONSOR HOVER FUNCTIONALITY
     */
    function initSubmenuHandling() {
        const submenuItems = document.querySelectorAll('.dropdown-submenu');

        submenuItems.forEach(submenuItem => {
            let submenuTimeout;
            const submenu = submenuItem.querySelector('.dropdown-menu');
            const submenuLink = submenuItem.querySelector('a');

            if (!submenu || !submenuLink) return;

            // Initially hide submenu
            submenu.style.opacity = '0';
            submenu.style.visibility = 'hidden';
            submenu.style.transform = 'translateX(-10px)';
            submenu.style.pointerEvents = 'none';

            // Show submenu on hover - SPONSOR HOVER EFFECT
            submenuItem.addEventListener('mouseenter', (e) => {
                clearTimeout(submenuTimeout);

                // Hide other submenus in the same dropdown
                const parentDropdown = submenuItem.closest('.dropdown-menu');
                if (parentDropdown) {
                    const otherSubmenus = parentDropdown.querySelectorAll('.dropdown-submenu .dropdown-menu');
                    otherSubmenus.forEach(otherSubmenu => {
                        if (otherSubmenu !== submenu) {
                            otherSubmenu.style.opacity = '0';
                            otherSubmenu.style.visibility = 'hidden';
                            otherSubmenu.style.transform = 'translateX(-10px)';
                            otherSubmenu.style.pointerEvents = 'none';
                        }
                    });
                }

                // Show this submenu
                submenu.style.opacity = '1';
                submenu.style.visibility = 'visible';
                submenu.style.transform = 'translateX(0)';
                submenu.style.pointerEvents = 'auto';

                console.log('Sponsor submenu shown'); // Debug log
            });

            // Hide submenu when leaving
            submenuItem.addEventListener('mouseleave', (e) => {
                // Check if we're moving to the submenu itself
                if (submenu.contains(e.relatedTarget)) return;

                submenuTimeout = setTimeout(() => {
                    submenu.style.opacity = '0';
                    submenu.style.visibility = 'hidden';
                    submenu.style.transform = 'translateX(-10px)';
                    submenu.style.pointerEvents = 'none';

                    console.log('Sponsor submenu hidden'); // Debug log
                }, 100);
            });

            // Keep submenu open when hovering over submenu itself
            submenu.addEventListener('mouseenter', () => {
                clearTimeout(submenuTimeout);
            });

            submenu.addEventListener('mouseleave', (e) => {
                // Check if we're moving back to parent submenu item
                if (submenuItem.contains(e.relatedTarget)) return;

                submenuTimeout = setTimeout(() => {
                    submenu.style.opacity = '0';
                    submenu.style.visibility = 'hidden';
                    submenu.style.transform = 'translateX(-10px)';
                    submenu.style.pointerEvents = 'none';
                }, 100);
            });
        });
    }



    /**
     * Clean up existing dropdown behaviors
     */
    function cleanupDropdowns() {
        // Reset all dropdown menus to default state
        document.querySelectorAll('.nav-item.dropdown .dropdown-menu').forEach(menu => {
            menu.style.opacity = '';
            menu.style.visibility = '';
            menu.style.transform = '';
            menu.style.pointerEvents = '';
        });
        
        // Remove initialization markers
        document.querySelectorAll('.nav-item.dropdown[data-dropdown-initialized]').forEach(dropdown => {
            dropdown.removeAttribute('data-dropdown-initialized');
        });
    }

    /**
     * Open mobile menu
     */
    function openMobileMenu() {
        if (state.isMobileMenuOpen) return;

        state.isMobileMenuOpen = true;

        // Add classes
        toggleClass(elements.mobileMenuOverlay, 'active', true);
        toggleClass(elements.mobileToggle, 'active', true);
        toggleClass(document.body, 'menu-open', true);

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Focus trap
        if (elements.mobileClose) {
            setTimeout(() => elements.mobileClose.focus(), CONFIG.ANIMATION_DURATION);
        }

        // Accessibility
        elements.mobileToggle.setAttribute('aria-expanded', 'true');
    }

    /**
     * Close mobile menu
     */
    function closeMobileMenu() {
        if (!state.isMobileMenuOpen) return;

        state.isMobileMenuOpen = false;

        // Remove classes
        toggleClass(elements.mobileMenuOverlay, 'active', false);
        toggleClass(elements.mobileToggle, 'active', false);
        toggleClass(document.body, 'menu-open', false);

        // Restore body scroll
        document.body.style.overflow = '';

        // Return focus to toggle button
        if (elements.mobileToggle) {
            setTimeout(() => elements.mobileToggle.focus(), CONFIG.ANIMATION_DURATION);
        }

        // Accessibility
        elements.mobileToggle.setAttribute('aria-expanded', 'false');
    }

    /**
     * Toggle mobile menu
     */
    function toggleMobileMenu() {
        if (state.isMobileMenuOpen) {
            closeMobileMenu();
        } else {
            openMobileMenu();
        }
    }

    /**
     * Toggle mobile dropdown submenu
     */
    function toggleMobileDropdown(event) {
        event.preventDefault();

        const dropdownToggle = event.target.closest('.mobile-dropdown-toggle');
        if (!dropdownToggle) return;

        const dropdown = dropdownToggle.closest('.mobile-dropdown');
        if (!dropdown) return;

        // Close other open dropdowns
        document.querySelectorAll('.mobile-dropdown.active').forEach(item => {
            if (item !== dropdown) {
                item.classList.remove('active');
            }
        });

        // Toggle current dropdown
        dropdown.classList.toggle('active');
    }

    // ========================================
    // Navigation Functions
    // ========================================

    /**
     * Handle navigation link clicks
     */
    function handleNavLinkClick(event) {
        const link = event.target.closest('.nav-link, .mobile-nav-link');
        if (!link) return;

        // Don't close menu if clicking on dropdown toggle
        if (link.classList.contains('mobile-dropdown-toggle')) {
            return;
        }

        const href = link.getAttribute('href');

        // Only close mobile menu for actual navigation links (not dropdown toggles)
        // and only for submenu items or main nav items without dropdowns
        const isSubmenuLink = link.closest('.mobile-submenu');
        const isMainNavWithoutDropdown = link.classList.contains('mobile-nav-link') &&
                                        !link.classList.contains('mobile-dropdown-toggle');

        if (state.isMobileMenuOpen && (isSubmenuLink || isMainNavWithoutDropdown)) {
            closeMobileMenu();
        }

        // Handle internal links (anchors)
        if (href && href.startsWith('#')) {
            event.preventDefault();

            const targetId = href.substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                // Smooth scroll to target
                const headerHeight = elements.header ? elements.header.offsetHeight : 0;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });

                // Update active nav link
                updateActiveNavLink(link);
            }
        }
    }

    /**
     * Update active navigation link
     */
    function updateActiveNavLink(activeLink) {
        // Remove active class from all nav links
        document.querySelectorAll('.nav-link.active').forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to clicked link
        if (activeLink && activeLink.classList.contains('nav-link')) {
            activeLink.classList.add('active');
        }    }

    // ========================================
    // Event Listeners
    // ========================================

    /**
     * Initialize all event listeners
     */
    function initEventListeners() {
        // Scroll events
        window.addEventListener('scroll', throttle(handleHeaderScroll, CONFIG.DEBOUNCE_DELAY));

        // Resize events
        window.addEventListener('resize', debounce(() => {
            // Close mobile menu on resize to desktop
            if (!isMobile() && state.isMobileMenuOpen) {
                closeMobileMenu();
            }
        }, 250));

        // Mobile toggle click
        if (elements.mobileToggle) {
            elements.mobileToggle.addEventListener('click', toggleMobileMenu);
        }

        // Mobile close button
        if (elements.mobileClose) {
            elements.mobileClose.addEventListener('click', closeMobileMenu);
        }

        // Mobile menu overlay click
        if (elements.mobileMenuOverlay) {
            elements.mobileMenuOverlay.addEventListener('click', (event) => {
                if (event.target === elements.mobileMenuOverlay) {
                    closeMobileMenu();
                }
            });
        }

        // Navigation clicks
        document.addEventListener('click', handleNavLinkClick);

        // Mobile dropdown toggles
        document.addEventListener('click', (event) => {
            if (event.target.closest('.mobile-dropdown-toggle')) {
                toggleMobileDropdown(event);
            }
        });

        // Mobile submenu links
        document.addEventListener('click', (event) => {
            const submenuLink = event.target.closest('.mobile-submenu a');
            if (submenuLink && state.isMobileMenuOpen) {
                // Close mobile menu when clicking submenu items
                setTimeout(() => {
                    closeMobileMenu();
                }, 100);
            }
        });

        // Keyboard events
        document.addEventListener('keydown', (event) => {
            // Escape key closes mobile menu
            if (event.key === 'Escape' && state.isMobileMenuOpen) {
                closeMobileMenu();
            }

            // Enter/Space on mobile toggle
            if ((event.key === 'Enter' || event.key === ' ') &&
                event.target === elements.mobileToggle) {
                event.preventDefault();
                toggleMobileMenu();
            }
        });
    }

    // ========================================
    // Intersection Observer for Active Links
    // ========================================

    /**
     * Initialize intersection observer for updating active nav links
     */
    function initIntersectionObserver() {
        const sections = document.querySelectorAll('section[id]');

        if (sections.length === 0) return;

        const observerOptions = {
            rootMargin: '-20% 0px -70% 0px',
            threshold: 0
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const id = entry.target.getAttribute('id');
                    const correspondingNavLink = document.querySelector(`.nav-link[href="#${id}"]`);

                    if (correspondingNavLink) {
                        updateActiveNavLink(correspondingNavLink);
                    }
                }
            });
        }, observerOptions);

        sections.forEach(section => observer.observe(section));
    }

    // ========================================
    // Touch Handling for Mobile
    // ========================================

    /**
     * Initialize touch handling for better mobile experience
     */
    function initTouchHandling() {
        // Add touch-specific event handlers
        let touchStartY = 0;
        let touchEndY = 0;

        // Handle swipe gestures on mobile menu
        if (elements.mobileMenu) {
            elements.mobileMenu.addEventListener('touchstart', (e) => {
                touchStartY = e.changedTouches[0].screenY;
            });

            elements.mobileMenu.addEventListener('touchend', (e) => {
                touchEndY = e.changedTouches[0].screenY;
                handleSwipeGesture();
            });
        }

        function handleSwipeGesture() {
            // Close menu on swipe down (if at top of menu)
            if (touchEndY > touchStartY + 100 && elements.mobileMenu.scrollTop === 0) {
                closeMobileMenu();
            }
        }
    }

    // ========================================
    // Performance Optimizations
    // ========================================

    /**
     * Preload critical resources
     */
    function preloadResources() {
        const criticalImages = [
            // Add any critical images here
        ];

        criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }

    /**
     * Initialize service worker for caching (if needed)
     */
    function initServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    }

    // ========================================
    // Accessibility Enhancements
    // ========================================

    /**
     * Initialize accessibility features
     */
    function initAccessibility() {
        // Add ARIA attributes
        if (elements.mobileToggle) {
            elements.mobileToggle.setAttribute('aria-expanded', 'false');
            elements.mobileToggle.setAttribute('aria-controls', 'mobileMenuOverlay');
            elements.mobileToggle.setAttribute('aria-label', 'Toggle navigation menu');
        }

        // Add skip link functionality
        const skipLink = document.querySelector('.skip-link');
        if (skipLink) {
            skipLink.addEventListener('click', (event) => {
                event.preventDefault();
                const target = document.querySelector(skipLink.getAttribute('href'));
                if (target) {
                    target.focus();
                    target.scrollIntoView();
                }
            });
        }

        // Handle reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.documentElement.style.setProperty('--transition-fast', '0.01ms');
            document.documentElement.style.setProperty('--transition-normal', '0.01ms');
            document.documentElement.style.setProperty('--transition-slow', '0.01ms');
        }
    }

    // ========================================
    // Animation Observers
    // ========================================

    /**
     * Initialize animation observers for elements that should animate when they come into view
     */
    function initAnimationObservers() {
        // Registration table animation
        const feesTable = document.querySelector('.modern-fees-table');
        
        if (feesTable) {
            const feesTableObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-visible');
                        feesTableObserver.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.2 });
            
            feesTableObserver.observe(feesTable);
        }
    }

    // ========================================
    // DOM Content Loaded
    // ========================================

    /**
     * Initialize everything when DOM is ready
     */
    function initializeApp() {
        // Cache DOM elements
        elements.header = document.getElementById('header');
        elements.mainNav = document.getElementById('mainNav');
        elements.mobileToggle = document.getElementById('mobileToggle');
        elements.mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
        elements.mobileMenu = document.querySelector('.mobile-menu');
        elements.mobileClose = document.getElementById('mobileClose');
        elements.navLinks = document.querySelectorAll('.nav-link');
        elements.dropdowns = document.querySelectorAll('.dropdown');

        // Initialize components
        initEventListeners();
        initDropdownHandling();
        initSubmenuHandling(); // Add submenu handling for Sponsors

        // Ensure all mobile dropdowns are closed on page load
        document.querySelectorAll('.mobile-dropdown').forEach(dropdown => {
            dropdown.classList.remove('active');
        });
        initAccessibility();
        initTouchHandling();
        preloadResources();
        
        // Initialize about section components
        initSecretariatCarousel();
        initAboutSectionInteractions();
        
        // Initialize animation observers
        initAnimationObservers();

        // Initialize intersection observer after a short delay
        setTimeout(initIntersectionObserver, 100);

        // Initial scroll check
        handleHeaderScroll();

        console.log('IMA NATCON 2025 website initialized successfully');
    }

    // ========================================
    // Error Handling
    // ========================================

    /**
     * Global error handler
     */
    window.addEventListener('error', (event) => {
        console.error('JavaScript Error:', event.error);
        // You could send this to an error reporting service
    });

    /**
     * Unhandled promise rejection handler
     */
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled Promise Rejection:', event.reason);
        // You could send this to an error reporting service
    });

    // ========================================
    // Initialization
    // ========================================

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
        initializeApp();
    }

    // ========================================
    // Public API (if needed)
    // ========================================

    // Expose some functions globally if needed by other scripts
    window.IMANatcon = {
        openMobileMenu,
        closeMobileMenu,
        updateActiveNavLink
    };

    // ==========================================
    // About Section - Secretariat Carousel
    // ==========================================
    
    let currentSecretariatSlide = 0;
    let secretariatSlides = [];
    let totalSecretariatSlides = 0;
    let secretariatAutoSlideInterval;

    /**
     * Initialize secretariat carousel
     */
    function initSecretariatCarousel() {
        secretariatSlides = document.querySelectorAll('.secretariat-slide');
        totalSecretariatSlides = secretariatSlides.length;
        
        if (totalSecretariatSlides > 0) {
            showSecretariatSlide(0);
            // Auto-rotate every 5 seconds
            secretariatAutoSlideInterval = setInterval(() => {
                changeSecretariatSlide(1);
            }, 5000);
        }
    }

    /**
     * Show specific secretariat slide
     */
    function showSecretariatSlide(index) {
        secretariatSlides.forEach((slide, i) => {
            slide.classList.toggle('active', i === index);
        });
    }

    /**
     * Change secretariat slide - Make this function global
     */
    window.changeSecretariatSlide = function(direction) {
        currentSecretariatSlide += direction;
        
        if (currentSecretariatSlide >= totalSecretariatSlides) {
            currentSecretariatSlide = 0;
        } else if (currentSecretariatSlide < 0) {
            currentSecretariatSlide = totalSecretariatSlides - 1;
        }
        
        showSecretariatSlide(currentSecretariatSlide);
        
        // Reset auto-slide timer
        if (secretariatAutoSlideInterval) {
            clearInterval(secretariatAutoSlideInterval);
            secretariatAutoSlideInterval = setInterval(() => {
                changeSecretariatSlide(1);
            }, 5000);
        }
    };

    // ========================================
    // About Section - Smooth Scrolling
    // ========================================
    
    /**
     * Initialize smooth scrolling for about section links
     */
    function initAboutSectionInteractions() {
        // Add smooth scrolling to quick links
        document.querySelectorAll('.quick-link').forEach(link => {
            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                if (href && href.startsWith('#')) {
                    e.preventDefault();
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }
            });
        });
        
        // Add smooth scroll for contact action buttons
        document.querySelectorAll('.contact-action-btn').forEach(btn => {
            const href = btn.getAttribute('href');
            if (href && href.startsWith('#')) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            }
        });
    }

    // ========================================
    // Committee Page Functions
    // ========================================
    
    /**
     * Initialize committee card animations and interactions
     */
    function initCommitteeCards() {
        const committeeCards = document.querySelectorAll('.committee-card, .chief-patron-card');
        
        if (committeeCards.length === 0) return;
        
        // Add hover effect animation
        committeeCards.forEach((card, index) => {
            // Set initial state for staggered animation
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.5s cubic-bezier(0.215, 0.61, 0.355, 1)';
            
            // Enhanced hover effects
            card.addEventListener('mouseenter', function() {
                this.style.transition = 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
            });
            
            // Add subtle animation when cards come into view
            animateCardOnScroll(card, index);
        });
        
        // Initialize social icons hover effects
        const socialIcons = document.querySelectorAll('.social-icon');
        socialIcons.forEach(icon => {
            icon.addEventListener('mouseenter', function() {
                this.style.transition = 'all 0.3s ease';
            });
        });
    }
    
    /**
     * Enhanced animation for committee cards with staggered reveal
     */
    function animateCardOnScroll(element, index) {
        if (!element) return;
        
        // Create intersection observer
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Calculate a staggered delay based on the card's index
                    const staggerDelay = Math.min(index * 120, 600); // Max 600ms delay
                    
                    // Add animation when element comes into view
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, staggerDelay);
                    
                    // Unobserve after animation
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.15, rootMargin: '0px 0px -50px 0px' });
        
        // Start observing
        observer.observe(element);
    }

    // ========================================
    // Initialization
    // ========================================

    /**
     * Initialize all components
     */
    function init() {
        // Cache DOM elements
        cacheElements();
        
        // Set up event listeners
        setupEventListeners();
        
        // Initialize scroll behavior
        initScrollBehavior();
        
        // Initialize dropdowns
        initDropdowns();
        
        // Initialize committee cards if on committee page
        initCommitteeCards();
    }

    // ==========================================
    // Downloads Modal
    // ==========================================
    
    function openDownloadsModal() {
        const modal = document.getElementById('downloadsModal');
        if (modal) {
            modal.style.display = 'block';
            // Force reflow
            modal.offsetHeight;
            // Add opacity transition
            modal.style.opacity = '1';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }
    }

    function closeDownloadsModal() {
        const modal = document.getElementById('downloadsModal');
        if (modal) {
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 150); // Match CSS transition duration
            document.body.style.overflow = 'auto'; // Restore scrolling
        }
    }

    // Make functions globally available
    window.openDownloadsModal = openDownloadsModal;
    window.closeDownloadsModal = closeDownloadsModal;

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        const downloadsModal = document.getElementById('downloadsModal');
        if (downloadsModal && event.target === downloadsModal) {
            closeDownloadsModal();
        }

        const historyModal = document.getElementById('historyModal');
        if (historyModal && event.target === historyModal) {
            closeHistoryModal();
        }

        const archiveModal = document.getElementById('archiveModal');
        if (archiveModal && event.target === archiveModal) {
            closeArchiveModal();
        }
    });
    
    // ==========================================
    // History Modal
    // ==========================================
    
    function openHistoryModal() {
        const modal = document.getElementById('historyModal');
        if (modal) {
            modal.style.display = 'block';
            // Force reflow
            modal.offsetHeight;
            // Add opacity transition
            modal.style.opacity = '1';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }
    }

    function closeHistoryModal() {
        const modal = document.getElementById('historyModal');
        if (modal) {
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 150); // Match CSS transition duration
            document.body.style.overflow = 'auto'; // Restore scrolling
        }
    }

    // Make functions globally available
    window.openHistoryModal = openHistoryModal;
    window.closeHistoryModal = closeHistoryModal;

    // ==========================================
    // Archive Modal
    // ==========================================

    function openArchiveModal() {
        const modal = document.getElementById('archiveModal');
        if (modal) {
            modal.style.display = 'block';
            // Force reflow
            modal.offsetHeight;
            // Add opacity transition
            modal.style.opacity = '1';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }
    }

    function closeArchiveModal() {
        const modal = document.getElementById('archiveModal');
        if (modal) {
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 150); // Match CSS transition duration
            document.body.style.overflow = 'auto'; // Restore scrolling
        }
    }

    // Make functions globally available
    window.openArchiveModal = openArchiveModal;
    window.closeArchiveModal = closeArchiveModal;

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const downloadsModal = document.getElementById('downloadsModal');
            if (downloadsModal && downloadsModal.style.display === 'block') {
                closeDownloadsModal();
            }

            const historyModal = document.getElementById('historyModal');
            if (historyModal && historyModal.style.display === 'block') {
                closeHistoryModal();
            }

            const archiveModal = document.getElementById('archiveModal');
            if (archiveModal && archiveModal.style.display === 'block') {
                closeArchiveModal();
            }
        }
    });
    
    // Initialize everything when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        init();
    });
})();